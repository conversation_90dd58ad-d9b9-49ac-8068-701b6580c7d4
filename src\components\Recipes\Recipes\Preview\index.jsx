'use client';
import React, { useRef, useEffect, useState, useContext } from 'react';
import { useParams, usePathname, useRouter } from 'next/navigation';
import AuthContext from '@/helper/authcontext';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
// import PreviewModeSelector from './components/PreviewModeSelector';
import RecipeHeader from './components/RecipeHeader';
import IngredientsCard from './components/IngredientsCard';
import CostAnalysisCard from './components/CostAnalysisCard';
import InstructionsCard from './components/InstructionsCard';
import ChefTipsGrid from './components/ChefTipsGrid';
import ServingInfoCard from './components/ServingInfoCard';
import ContactCard from './components/ContactCard';
import { useReactToPrint } from 'react-to-print';
import {
  exportRecipe,
  getRecipePreviewData,
  trackRecipeView,
  getRecipeSettings,
} from '@/services/recipeService';
import { isPublicRoute, setApiMessage } from '@/helper/common/commonFunctions';
import ContentLoader from '@/components/UI/ContentLoader';
import { Tooltip, Typography } from '@mui/material';
import NoDataView from '@/components/UI/NoDataView';
import ScalingCard from './components/ScalingCard';
import HaccpCard from './components/HaccpCard';
import PreLoader from '@/components/UI/Loader';
import { removeFromStorage, saveToStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import CommonNoDataImage from '../CommonNoDataImage';
import './preview.scss';

const scalingOptions = [
  { value: 0.25, label: 'One Quarter (¼)' },
  { value: 1 / 3, label: 'One Third (⅓)' },
  { value: 0.5, label: 'One Half (½)' },
  { value: 2 / 3, label: 'Two Thirds (⅔)' },
  { value: 0.75, label: 'Three Quarters (¾)' },
  { value: 1, label: 'Default (1x)' },
  { value: 1.5, label: 'One and One Half (1 ½)' },
  { value: 2, label: 'Double (2x)' },
  { value: 3, label: 'Triple (3x)' },
  { value: 4, label: 'Quadruple (4x)' },
  { value: 'custom', label: 'Custom' },
];

const RecipePreviewView = ({ slug }) => {
  const printRef = useRef();
  const pathname = usePathname();
  const router = useRouter();
  const { org_name } = useParams();
  const { authState } = useContext(AuthContext);
  const [recipeData, setRecipeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [scalingType, setScalingType] = useState(1); // default to 1x
  const [customValue, setCustomValue] = useState(1);
  const [exportLoading, setExportLoading] = useState(false);
  const [highlightEnabled, setHighlightEnabled] = useState(true); // default true for backward compatibility

  // Get recipe permission
  const getRecipesPermission = authState?.UserPermission?.['recipe'];
  const isAllPermission = getRecipesPermission === 2;

  // Helper function to determine if this is a public page
  const isPublicPage = pathname?.includes(
    `/recipe/${org_name}/recipe-preview/`
  );

  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.getElementById('public-recipe-header');
      if (header) {
        document.documentElement.style.setProperty(
          '--public-recipe-header-height',
          `${header.offsetHeight}px`
        );
      }
    };

    updateHeaderHeight();
    window.addEventListener('resize', updateHeaderHeight);
    return () => window.removeEventListener('resize', updateHeaderHeight);
  }, []);

  useEffect(() => {
    // Fetch settings for highlight control
    const fetchSettings = async () => {
      try {
        const data = await getRecipeSettings();
        setHighlightEnabled(
          data?.privateRecipeVisibilitySettings?.highlightChanges !== undefined
            ? data?.privateRecipeVisibilitySettings?.highlightChanges
            : true // fallback to true if not set
        );
      } catch {
        setHighlightEnabled(true); // fallback to true on error
      }
    };
    if (!isPublicPage) {
      fetchSettings();
    }
  }, []);

  // Helper function to determine if a component should be highlighted
  const getHighlightData = (componentType) => {
    if (isPublicPage) return null;
    if (!highlightEnabled) return null;

    const highlights = recipeData?.categorized_history?.updated_fields;
    if (!highlights || typeof highlights !== 'object') return null;

    // Map component types to highlight keys based on actual component usage
    const componentMap = {
      // RecipeHeader component keys
      header: [
        'recipe_title',
        'recipe_description',
        'recipe_cook_time',
        'recipe_preparation_time',
        'recipe_yield',
        'recipe_total_portions',
        'recipe_single_portion_size',
        'recipe_complexity_level',
        'categories',
        'allergen_attributes',
        'nutrition_attributes',
        'dietary_attributes',
      ],

      // ServingInfoCard component keys
      serving_info: [
        'recipe_serving_method',
        'recipe_serve_in',
        'recipe_garnish',
      ],

      // ChefTipsGrid component keys
      chef_tips: ['recipe_head_chef_tips', 'recipe_foh_tips'],

      // InstructionsCard component keys
      instructions: ['steps'],

      // IngredientsCard component keys
      ingredients: ['ingredients'],

      // CostAnalysisCard component keys
      cost: ['cost'],

      // HaccpCard component keys
      haccp: ['haccp_attributes'],

      // Categories highlight
      categories: ['categories'],

      // Nutrition attributes highlight
      nutrition_attributes: ['nutrition_attributes'],

      // Allergen attributes highlight
      allergen_attributes: ['allergen_attributes'],

      // Dietary attributes highlight
      dietary_attributes: ['dietary_attributes'],

      // Cuisine attributes highlight
      cuisine_attributes: ['cuisine_attributes'],

      // General keys for components that use multiple highlight types
      general: [
        'recipe_description',
        'recipe_preparation_time',
        'recipe_complexity_level',
        'recipe_title',
        'recipe_cook_time',
        'recipe_yield',
        'recipe_total_portions',
        'recipe_single_portion_size',
        'recipe_serving_method',
        'recipe_serve_in',
        'recipe_garnish',
        'recipe_head_chef_tips',
        'recipe_foh_tips',
        'steps',
        'ingredients',
        'cost',
        'haccp',
        'categories',
        'nutrition_attributes',
        'allergen_attributes',
        'dietary_attributes',
        'cuisine_attributes',
      ],
    };

    // Check if any relevant keys exist for the given component type
    const relevantKeys = componentMap?.[componentType];
    if (!relevantKeys) {
      // If no specific mapping found, return the entire highlight object
      return highlights;
    }
    const hasMatch = relevantKeys?.some((key) => key in highlights);

    return hasMatch ? highlights : null;
  };

  // Helper function to check if chef tips data exists
  const hasChefTipsData = () => {
    return recipeData?.recipe_head_chef_tips || recipeData?.recipe_foh_tips;
  };

  // Helper function to check if serving info data exists
  const hasServingInfoData = () => {
    return (
      recipeData?.recipe_serving_method ||
      recipeData?.recipe_serve_in ||
      recipeData?.recipe_garnish
    );
  };

  // Helper function to check if contact card data exists
  const hasContactData = () => {
    const contactSettings =
      recipeData?.organization_settings?.publicRecipeCallToAction;

    if (!contactSettings || contactSettings?.none) {
      return false;
    }

    // Check if any contact option is enabled and has data
    const hasContactForm = contactSettings?.contactForm;
    const hasContactInfo =
      contactSettings?.contactInfo?.enabled &&
      (contactSettings?.contactInfo?.name ||
        contactSettings?.contactInfo?.phone ||
        contactSettings?.contactInfo?.email ||
        contactSettings?.contactInfo?.link);
    const hasCustomCta =
      contactSettings?.customCtaLink?.enabled &&
      contactSettings?.customCtaLink?.text &&
      contactSettings?.customCtaLink?.link;

    return hasContactForm || hasContactInfo || hasCustomCta;
  };

  const recipeAddedFields = recipeData?.categorized_history?.added_fields || '';
  const recipeRemovedFields =
    recipeData?.categorized_history?.removed_fields || '';

  useEffect(() => {
    removeFromStorage(identifiers.RECIPE_PUBLIC_ORG_DATA);
    const fetchRecipeData = async () => {
      if (!slug) return;

      try {
        setIsLoading(true);
        // Use the service function that handles public/private route logic
        const response = await getRecipePreviewData(slug, pathname);
        const { singleRecipe, organization_details } = response;
        const isPublic = isPublicRoute(pathname);
        if (isPublic) {
          saveToStorage(
            identifiers.RECIPE_PUBLIC_ORG_DATA,
            organization_details || ''
          );
        }
        setRecipeData(singleRecipe);

        // Merge removed_fields into updated_fields for highlighting
        if (
          singleRecipe?.categorized_history?.removed_fields &&
          singleRecipe?.categorized_history?.updated_fields
        ) {
          const removedFields = singleRecipe.categorized_history.removed_fields;
          const updatedFields = singleRecipe.categorized_history.updated_fields;
          Object.keys(removedFields).forEach((key) => {
            const removedValue = removedFields[key];
            if (key in updatedFields) {
              // If both are arrays, merge unique values
              if (
                Array.isArray(updatedFields[key]) &&
                Array.isArray(removedValue)
              ) {
                const existingIds = new Set(
                  updatedFields[key].map((item) => item.id)
                );
                removedValue.forEach((item) => {
                  if (!existingIds.has(item.id)) {
                    updatedFields[key].push(item);
                  }
                });
              }
              // If not arrays, do not overwrite
            } else {
              // Add the key if not present
              updatedFields[key] = Array.isArray(removedValue)
                ? [...removedValue]
                : removedValue;
            }
          });
        }

        // Track recipe view for analytics on public recipe views
        if (isPublicPage && singleRecipe?.id) {
          await trackRecipeView({
            recipe_id: singleRecipe?.id,
            recipe_name:
              singleRecipe?.recipe_public_title || singleRecipe?.recipe_title,
          });
        }
      } catch (error) {
        if (error?.response?.status === 404) {
          router.push(pathname + '/not-found');
          return;
        }
        setApiMessage('error', error?.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecipeData();
  }, [slug, pathname, isPublicPage]);

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'Recipe Preview',
    removeAfterPrint: true,
  });

  const handleExport = async (recipe) => {
    // Handle export functionality for recipe
    setExportLoading(true);
    try {
      const response = await exportRecipe(recipe?.id, 'pdf');

      // Get filename from response headers or fallback
      let filename = `${recipe?.recipe_title || 'recipe'}.pdf`;
      const disposition = response?.headers?.['content-disposition'];
      if (disposition) {
        const match = disposition.match(/filename="?([^";]+)"?/);
        if (match) filename = match[1];
      }
      // Create blob and trigger download
      const url = window?.URL?.createObjectURL(new Blob([response?.data]));
      const link = document?.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document?.body?.appendChild(link);
      link.click();
      link?.parentNode?.removeChild(link);
      window?.URL?.revokeObjectURL(url);
      setExportLoading(false);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setExportLoading(false);
    }
  };

  const handleBack = () => {
    // Determine proper back navigation based on current route
    if (isPublicPage) {
      // For public pages, go to public recipes list or home
      router.push('/');
    } else {
      // For private pages, go to recipes list
      router.push('/recipes');
    }
  };

  if (isLoading) {
    return (
      <div style={{ paddingTop: 'var(--public-recipe-header-height)' }}>
        <ContentLoader />
      </div>
    );
  }

  if (!recipeData) {
    return (
      <div style={{ paddingTop: 'var(--public-recipe-header-height)' }}>
        <NoDataView
          image={<CommonNoDataImage />}
          title="Recipe Not Found"
          description="The requested recipe could not be found. Please check the URL and try again."
          className="no-data-auto-margin-height-conainer"
        />
      </div>
    );
  }

  return (
    <div
      className="recipe-preview"
      style={{ paddingTop: 'var(--public-recipe-header-height)' }}
    >
      {exportLoading && <PreLoader />}
      {/* Header Controls */}
      {!isPublicPage && (
        <div className="recipe-preview__header">
          <div className="recipe-preview__header-content">
            <div className="recipe-preview__header-left">
              <CustomButton
                variant="outlined"
                title="Back"
                leftIcon={<Icon name="ArrowLeft" size={16} />}
                onClick={handleBack}
                className="recipe-preview__action-btn recipe-preview__back-btn"
              />
              {/* <PreviewModeSelector /> */}
            </div>

            <div className="recipe-preview__header-actions">
              {/* Only show all action buttons on private pages */}
              {!isPublicPage && (
                <>
                  {/* Staff Tracking Button */}
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Staff Tracking
                      </Typography>
                    }
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <span>
                      {isAllPermission && (
                        <CustomButton
                          variant="outlined"
                          isIconOnly
                          startIcon={<Icon name="List" size={18} />}
                          onClick={() =>
                            router.push(
                              `/recipes/staff-tracking?recipeId=${recipeData?.id || 'demo'}`
                            )
                          }
                          className="staff-tracking-btn"
                        />
                      )}
                    </span>
                  </Tooltip>

                  <CustomButton
                    variant="outlined"
                    title="Print"
                    leftIcon={<Icon name="Printer" size={16} />}
                    onClick={handlePrint}
                    className="recipe-preview__action-btn"
                  />

                  <CustomButton
                    variant="contained"
                    title="Export"
                    leftIcon={<Icon name="Download" size={16} />}
                    onClick={() => handleExport(recipeData)}
                    className="recipe-preview__action-btn"
                  />
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Recipe Content */}
      <div ref={printRef}>
        <div
          className="recipe-preview__container"
          style={{ marginTop: isPublicPage ? '45px' : '0px' }}
        >
          <RecipeHeader
            recipeData={recipeData}
            isPublicPage={isPublicPage}
            highlightData={getHighlightData('header')}
            isHighlightingActive={
              !!recipeData?.categorized_history?.updated_fields &&
              highlightEnabled
            }
            addedFields={recipeAddedFields}
            removedFields={recipeRemovedFields}
          />

          <div className="recipe-preview__content-grid">
            {/* Right Column - Instructions & Chef Notes (now positioned left) */}
            <div className="recipe-preview__right-column">
              {/* Show preparation steps: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage && recipeData?.steps?.length > 0)) && (
                <InstructionsCard
                  instructions={recipeData?.steps}
                  highlightData={getHighlightData('instructions')}
                  addedFields={recipeAddedFields}
                  removedFields={recipeRemovedFields}
                />
              )}

              {/* Show chef tips: always for private pages, only if data exists for public pages */}
              {(!isPublicPage || (isPublicPage && hasChefTipsData())) &&
                hasChefTipsData() && (
                  <ChefTipsGrid
                    recipeData={recipeData}
                    highlightData={getHighlightData('chef_tips')}
                    addedFields={recipeAddedFields}
                    removedFields={recipeRemovedFields}
                  />
                )}

              {/* Show serving info: always for private pages, only if data exists for public pages */}
              {(!isPublicPage || (isPublicPage && hasServingInfoData())) &&
                hasServingInfoData() && (
                  <ServingInfoCard
                    recipeData={recipeData}
                    isPublicPage={isPublicPage}
                    highlightData={getHighlightData('serving_info')}
                    addedFields={recipeAddedFields}
                    removedFields={recipeRemovedFields}
                  />
                )}

              {/* Show haccp: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage && recipeData?.haccp_attributes?.length > 0)) && (
                <HaccpCard
                  haccpData={recipeData?.haccp_attributes}
                  highlightData={getHighlightData('haccp')}
                  addedFields={recipeAddedFields}
                  removedFields={recipeRemovedFields}
                />
              )}
            </div>

            {/* Left Column - Ingredients & Cost (now positioned right) */}
            <div className="recipe-preview__left-column">
              {/* Show scaling: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage &&
                  recipeData?.organization_settings
                    ?.recipeDetailsToDisplayPublicly?.scale)) && (
                <ScalingCard
                  scalingType={scalingType}
                  customValue={customValue}
                  scalingOptions={scalingOptions}
                  onScalingTypeChange={(type) => {
                    setScalingType(type);
                    if (type !== 'custom') setCustomValue(1);
                  }}
                  onCustomValueChange={setCustomValue}
                />
              )}

              {/* Show ingredients: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage && recipeData?.ingredients?.length > 0)) && (
                <IngredientsCard
                  ingredients={recipeData?.ingredients}
                  isPublicPage={isPublicPage}
                  highlightData={getHighlightData('ingredients')}
                  isHighlightingActive={
                    !!recipeData?.categorized_history?.updated_fields &&
                    highlightEnabled
                  }
                  scaling={scalingType === 'custom' ? customValue : scalingType}
                  addedFields={recipeAddedFields}
                  removedFields={recipeRemovedFields}
                />
              )}

              {/* Show contact card only on public pages and only if contact data exists */}
              {isPublicPage && hasContactData() && (
                <ContactCard recipeData={recipeData} />
              )}

              {/* Show cost: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage &&
                  recipeData?.ingredients?.length > 0 &&
                  recipeData?.ingredients?.some(
                    (ingredient) => ingredient?.ingredient_cost > 0
                  ) &&
                  recipeData?.organization_settings
                    ?.recipeDetailsToDisplayPublicly?.cost)) && (
                <CostAnalysisCard
                  recipeData={recipeData}
                  highlightData={getHighlightData('cost')}
                  scaling={scalingType === 'custom' ? customValue : scalingType}
                  addedFields={recipeAddedFields}
                  removedFields={recipeRemovedFields}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecipePreviewView;
