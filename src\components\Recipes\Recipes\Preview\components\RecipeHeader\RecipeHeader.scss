.recipe-header {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  border: var(--border-width-xs) solid var(--color-light-gray);
  padding: var(--spacing-lg) var(--spacing-xxl);
  margin-bottom: var(--spacing-xxl);
  box-shadow: var(--box-shadow-xs);

  &__back-section {
    // margin-bottom: var(--spacing-lg);
    // padding-bottom: var(--spacing-lg);
    // border-bottom: var(--border-width-xs) solid var(--color-light-gray);
    position: absolute;
    top: -30px;
    left: 24px;
    @media (max-width: 1024px) {
      left: 16px;
    }
  }

  &__back-btn {
    background: var(--color-white);
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);

    @media (min-width: 1201px) {
      flex-direction: row;
      align-items: flex-start;
      justify-content: space-between;
    }
  }

  &__info {
    width: 58%;
    @media (max-width: 1200px) {
      width: 100%;
    }
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    margin-bottom: var(--spacing-xs);
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-sm);
  }

  // Categories
  &__categories {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__category-tag {
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: var(--color-primary-opacity);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-primary);
    position: relative;

    &--highlight {
      padding: calc(var(--spacing-xs) * 0.7) calc(var(--spacing-md) * 0.7);
      background-color: var(--color-light-gray);
      border-radius: calc(var(--border-radius-md) * 0.8);
    }
  }

  &__dietary-tag {
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: var(--color-success-opacity);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-success);
    position: relative;

    &--highlight {
      padding: calc(var(--spacing-xs) * 0.7) calc(var(--spacing-md) * 0.7);
      background-color: var(--color-light-gray);
      border-radius: calc(var(--border-radius-md) * 0.8);
    }
  }

  // Stats Section
  &__stats {
    width: 42%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    @media (max-width: 1200px) {
      width: 100%;
    }
  }

  &__stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    text-align: center;

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__stat-card {
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-xs);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);

    &--time {
      background-color: var(--color-warning-opacity);
    }

    &--portions {
      background-color: var(--color-success-opacity);
    }

    &--cost {
      background-color: var(--color-danger-opacity);
    }

    &--size {
      background-color: var(--color-warning-opacity);
    }
  }

  .old-allergen-icons-wrap {
    margin-left: var(--spacing-xxl);
  }

  &__allergen-icons-wrap {
    line-height: 0px;
    display: flex;
    gap: var(--spacing-xsm);
  }
  &__highlight-allergen-icons-wrap {
    line-height: 0px;
    display: flex;
    gap: var(--spacing-xsm);
    margin-left: var(--spacing-xxl);
  }

  &__stat-value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__stat-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Allergen Warning
  &__allergen-warning {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-xsm);
  }

  &__difficulty {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    max-width: max-content;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);

    &--low {
      color: var(--color-success);
      background-color: var(--color-success-opacity);
    }

    &--medium {
      color: var(--color-warning);
      background-color: var(--color-warning-opacity);
    }

    &--hard {
      color: var(--color-danger);
      background-color: var(--color-danger-opacity);
    }

    &--default {
      color: var(--text-color-slate-gray);
      background-color: var(--color-light-gray);
    }
  }

  &__difficulty-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-sm);
  }

  &__allergen-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  &__allergen-icon {
    flex-shrink: 0;
    height: 16px;
    width: 16px;
  }

  &__allergen-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-sm);
    margin-bottom: var(--spacing-xs);
  }

  // Allergen Icons
  &__allergen-icons {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
  }

  &__allergen-icon-item {
    // height: 30px;
    // width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    // svg {
    //   height: 30px;
    //   width: 30px;
    // }
  }

  &__allergen-icon-wrapper {
    position: relative;
    display: inline-block;
  }

  &__may-allergen-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  &__may-allergen-indicator {
    position: absolute;
    top: -4px;
    right: -4px;
    background-color: var(--color-warning);
    color: var(--text-color-white);
    font-size: var(--font-size-xxs);
    font-weight: var(--font-weight-bold);
    width: 16px;
    height: 16px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--text-color-white);
    z-index: 10;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  &__may-allergen-new-removed-indicator {
    position: absolute;
    right: 0;
    top: 17px;
  }
  &__recipe-placeholder {
    width: 100%;
    height: 200px;
    object-fit: scale-down;
  }

  // Highlighting styles for allergen warning
  .highlight-container {
    .recipe-header__allergen-warning {
      &.highlight-no-margin-bottom {
        margin-bottom: 0;
      }
    }

    .highlight-original-allergen {
      opacity: 0.6;
      font-size: 0.9em;
      color: var(--text-color-slate-gray);
      padding-top: var(--spacing-xs);
      margin-top: var(--spacing-xs);
    }

    .highlight-original-allergen-icon-wrapper {
      opacity: 0.6;
      position: relative;
      display: inline-block;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background-color: var(--color-danger);
        transform: translateY(-50%);
        z-index: 1;
      }

      img {
        filter: grayscale(100%);
      }
    }

    .highlight-original-allergen-indicator {
      opacity: 0.6;
      background-color: var(--text-color-slate-gray) !important;
      color: var(--text-color-white) !important;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background-color: var(--color-danger);
        transform: translateY(-50%);
        z-index: 1;
      }
    }
  }
}
