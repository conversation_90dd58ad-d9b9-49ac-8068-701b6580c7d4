'use client';
import React, { useState } from 'react';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import { Box } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import Icon from '@/components/UI/AppIcon/AppIcon';

const departmentOptions = [
  { label: 'Select Department', value: '' },
  { label: 'HR', value: 'hr' },
  { label: 'Finance', value: 'finance' },
  { label: 'Operations', value: 'operations' },
];

const userOptions = [
  { label: 'Select User', value: '' },
  { label: '<PERSON>', value: 'john' },
  { label: '<PERSON>', value: 'jane' },
];

const periodOptions = [
  { label: 'Week', value: 'week' },
  { label: 'Month', value: 'month' },
  { label: 'Custom', value: 'custom' },
];

const shiftStatusOptions = [
  { label: 'Select Status', value: '' },
  { label: 'Published', value: 'published' },
  { label: 'Unpublished', value: 'unpublished' },
  { label: 'Open', value: 'open' },
  { label: 'Drop', value: 'drop' },
];
const columns = [
  { header: 'User Name', accessor: 'employee', sortable: true },
  { header: 'Role', accessor: 'role', sortable: true },
  { header: 'Branch / Department', accessor: 'department', sortable: true },
  { header: 'Total Shifts', accessor: 'shift', sortable: true },
  { header: 'Date', accessor: 'date', sortable: true },
];

const staticData = [
  {
    employee: 'John Smith',
    role: 'Sales Executive',
    department: 'Sales',
    shift: 1,
    date: '2024-01-15',
  },
  {
    employee: 'Sarah Johnson',
    role: 'Marketing Manager',
    department: 'Marketing',
    shift: 1,
    date: '2024-01-15',
  },
  {
    employee: 'Mike Wilson',
    role: 'IT Specialist',
    department: 'IT',
    shift: 1,
    date: '2024-01-16',
  },
  {
    employee: 'Emma Davis',
    role: 'HR Coordinator',
    department: 'HR',
    shift: 1,
    date: '2024-01-16',
  },
  {
    employee: 'David Lee',
    role: 'Finance Analyst',
    department: 'Finance',
    shift: 1,
    date: '2024-01-17',
  },
  {
    employee: 'Olivia Brown',
    role: 'Operations Officer',
    department: 'Operations',
    shift: 1,
    date: '2024-01-17',
  },
  {
    employee: 'James Miller',
    role: 'Sales Executive',
    department: 'Sales',
    shift: 1,
    date: '2024-01-18',
  },
  {
    employee: 'Sophia Wilson',
    role: 'Marketing Coordinator',
    department: 'Marketing',
    shift: 1,
    date: '2024-01-18',
  },
  {
    employee: 'Benjamin Clark',
    role: 'IT Support',
    department: 'IT',
    shift: 1,
    date: '2024-01-19',
  },
  {
    employee: 'Ava Martinez',
    role: 'HR Assistant',
    department: 'HR',
    shift: 1,
    date: '2024-01-19',
  },
  {
    employee: 'William Harris',
    role: 'Finance Manager',
    department: 'Finance',
    shift: 1,
    date: '2024-01-20',
  },
  {
    employee: 'Mia Lewis',
    role: 'Operations Supervisor',
    department: 'Operations',
    shift: 1,
    date: '2024-01-20',
  },
  {
    employee: 'Elijah Walker',
    role: 'Sales Lead',
    department: 'Sales',
    shift: 1,
    date: '2024-01-21',
  },
  {
    employee: 'Charlotte Hall',
    role: 'Marketing Specialist',
    department: 'Marketing',
    shift: 1,
    date: '2024-01-21',
  },
  {
    employee: 'Lucas Allen',
    role: 'Network Engineer',
    department: 'IT',
    shift: 1,
    date: '2024-01-22',
  },
  {
    employee: 'Amelia Young',
    role: 'HR Manager',
    department: 'HR',
    shift: 1,
    date: '2024-01-22',
  },
  {
    employee: 'Henry King',
    role: 'Accountant',
    department: 'Finance',
    shift: 1,
    date: '2024-01-23',
  },
  {
    employee: 'Emily Wright',
    role: 'Operations Analyst',
    department: 'Operations',
    shift: 1,
    date: '2024-01-23',
  },
  {
    employee: 'Jack Scott',
    role: 'Sales Consultant',
    department: 'Sales',
    shift: 1,
    date: '2024-01-24',
  },
  {
    employee: 'Grace Green',
    role: 'Marketing Executive',
    department: 'Marketing',
    shift: 1,
    date: '2024-01-24',
  },
  {
    employee: 'Daniel Adams',
    role: 'IT Technician',
    department: 'IT',
    shift: 1,
    date: '2024-01-25',
  },
  {
    employee: 'Ella Baker',
    role: 'HR Executive',
    department: 'HR',
    shift: 1,
    date: '2024-01-25',
  },
  {
    employee: 'Matthew Nelson',
    role: 'Financial Planner',
    department: 'Finance',
    shift: 1,
    date: '2024-01-26',
  },
  {
    employee: 'Scarlett Carter',
    role: 'Ops Coordinator',
    department: 'Operations',
    shift: 1,
    date: '2024-01-26',
  },
  {
    employee: 'Sebastian Perez',
    role: 'Sales Manager',
    department: 'Sales',
    shift: 1,
    date: '2024-01-27',
  },
  {
    employee: 'Chloe Roberts',
    role: 'Marketing Lead',
    department: 'Marketing',
    shift: 1,
    date: '2024-01-27',
  },
  {
    employee: 'Alexander Turner',
    role: 'Systems Admin',
    department: 'IT',
    shift: 1,
    date: '2024-01-28',
  },
  {
    employee: 'Lily Phillips',
    role: 'HR Specialist',
    department: 'HR',
    shift: 1,
    date: '2024-01-28',
  },
  {
    employee: 'Mason Campbell',
    role: 'Finance Clerk',
    department: 'Finance',
    shift: 1,
    date: '2024-01-29',
  },
  {
    employee: 'Zoe Parker',
    role: 'Operations Manager',
    department: 'Operations',
    shift: 1,
    date: '2024-01-29',
  },
];

// Menu items for each row
const menuItems = [
  {
    label: 'View',
    icon: <Icon name="Eye" size={16} />,
    onClick: (item, row) => {
      alert(`View details for ${row.employee}`);
      // Add navigation or modal logic here
    },
  },
];

export default function RotaReports() {
  const [filters, setFilters] = useState({ date_period: 'week' });
  const [filteredData, setFilteredData] = useState(staticData);

  // Helper functions to get week/month date range
  const getCurrentWeekRange = () => {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 (Sun) - 6 (Sat)
    const diffToMonday = (dayOfWeek + 6) % 7;
    const monday = new Date(now);
    monday.setDate(now.getDate() - diffToMonday);
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);
    return [monday, sunday];
  };
  const getCurrentMonthRange = () => {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    return [firstDay, lastDay];
  };

  // Dynamically build filter fields
  const filterFields = [
    {
      type: 'search',
      label: 'Search',
      name: 'search',
      placeholder: 'Enter Search',
    },
    {
      type: 'select',
      label: 'Branch',
      name: 'branch',
      options: departmentOptions,
      placeholder: 'Select Branch',
    },
    {
      type: 'select',
      label: 'Department',
      name: 'department',
      options: departmentOptions,
      placeholder: 'Select Department',
    },
    {
      type: 'select',
      label: 'Role',
      name: 'role',
      options: departmentOptions,
      placeholder: 'Select Role',
    },
    {
      type: 'select',
      label: 'User',
      name: 'user',
      options: userOptions,
      placeholder: 'Select User',
    },
    {
      type: 'select',
      label: 'Shift Status',
      name: 'shift_status',
      options: shiftStatusOptions,
      placeholder: 'Select Shift Status',
    },
    {
      type: 'select',
      label: 'Date',
      name: 'date_period',
      options: periodOptions,
      placeholder: 'Select Date',
    },
  ];

  // Insert date-range field if date_period is 'custom'
  if (filters.date_period === 'custom') {
    filterFields.push({
      type: 'date-range',
      label: 'Date Range',
      name: 'dateRange',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    });
  }

  // Handle filter changes
  const handleApplyFilters = (values) => {
    let newFilters = { ...values };
    // If date_period is week/month, set dateRange automatically
    if (values.date_period === 'week') {
      newFilters.dateRange = getCurrentWeekRange();
    } else if (values.date_period === 'month') {
      newFilters.dateRange = getCurrentMonthRange();
    }
    setFilters(newFilters);

    // Apply filters to the data
    let filtered = [...staticData];

    // Search filter
    if (newFilters.search) {
      const searchTerm = newFilters.search.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.employee.toLowerCase().includes(searchTerm) ||
          item.role.toLowerCase().includes(searchTerm) ||
          item.department.toLowerCase().includes(searchTerm) ||
          item.shift.toString().toLowerCase().includes(searchTerm)
      );
    }

    // Date range filter
    if (newFilters.dateRange && newFilters.dateRange.length === 2) {
      const startDate = new Date(newFilters.dateRange[0]);
      const endDate = new Date(newFilters.dateRange[1]);
      filtered = filtered.filter((item) => {
        const itemDate = new Date(item.date);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // Department filter
    if (newFilters.department) {
      filtered = filtered.filter(
        (item) =>
          item.department.toLowerCase() === newFilters.department.toLowerCase()
      );
    }

    // Other search filter
    if (newFilters.other_search) {
      const otherSearchTerm = newFilters.other_search.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.employee.toLowerCase().includes(otherSearchTerm) ||
          item.department.toLowerCase().includes(otherSearchTerm) ||
          item.shift.toLowerCase().includes(otherSearchTerm) ||
          item.hours.toLowerCase().includes(otherSearchTerm) ||
          item.status.toLowerCase().includes(otherSearchTerm)
      );
    }

    // User filter (assuming it filters by employee name)
    if (newFilters.user) {
      filtered = filtered.filter((item) =>
        item.employee.toLowerCase().includes(newFilters.user.toLowerCase())
      );
    }

    setFilteredData(filtered);
  };

  // Handle field change (especially for date_period)
  const handleFieldChange = (name, value) => {
    if (name === 'date_period') {
      let updatedFilters = { ...filters, date_period: value };
      if (value === 'custom') {
        updatedFilters.dateRange = [null, null];
      } else if (value === 'week') {
        updatedFilters.dateRange = getCurrentWeekRange();
      } else if (value === 'month') {
        updatedFilters.dateRange = getCurrentMonthRange();
      }
      setFilters(updatedFilters);
    } else {
      setFilters((prev) => ({ ...prev, [name]: value }));
    }
  };

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />
      {/* Your report content goes here */}

      <Box className="report-table-container">
        <CommonTable
          columns={columns}
          data={filteredData}
          pageSize={10}
          actionMenuItems={menuItems}
        />
      </Box>
    </Box>
  );
}
