'use client';
import React, { useState } from 'react';
import { Box } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import ContactSubmissionReportsTable from './ContactSubmissionReportsTable';
import '../reports.scss';

export default function ContactSubmissionReports() {
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState({
    dateRange: null,
  });

  // Filter fields configuration
  const filterFields = [
    {
      name: 'search',
      type: 'search',
      label: 'Search by Name',
      searchclass: 'search-field-wrapper',
    },
    {
      name: 'dateRange',
      type: 'date-range',
      label: 'Date Range',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    },
  ];

  // Handle filter apply
  const handleFilterApply = (filterValues) => {
    setFilters(filterValues);
    setSearchValue(filterValues?.search || '');
  };

  // Handle field change
  const handleFieldChange = (fieldName, value) => {
    if (fieldName === 'search') {
      setSearchValue(value);
    }
  };

  return (
    <Box className="report-main-container">
      {/* Filter Section */}
      <FilterCollapse
        fields={filterFields}
        onApply={handleFilterApply}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />

      {/* Table Section */}
      <ContactSubmissionReportsTable
        searchValue={searchValue}
        filters={filters}
      />
    </Box>
  );
}
