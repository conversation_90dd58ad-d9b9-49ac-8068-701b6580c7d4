import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';

export const leaveService = {
  // Get leave balance list
  getLeaveBalanceList: async (params = {}) => {
    try {
      const {
        search = '',
        page = 1,
        size = 10,
        branch_id = '',
        department_id = '',
        role_id = '',
        start_date = '',
        end_date = '',
        report_mode = 'day',
        leave_type_id = '',
        sort_by = '',
        sort_order = '',
        type = '',
      } = params;

      let url = `${URLS?.GET_LEAVE_BALANCE}?branch_id=${branch_id}&department_id=${department_id}&role_id=${role_id}&search=${search}&page=${page}&size=${size}&report_mode=${report_mode}`;

      // Add leave type id if provided
      if (leave_type_id) url += `&leave_type_id=${leave_type_id}`;

      // Add date range if provided
      if (start_date) url += `&start_date=${start_date}`;
      if (end_date) url += `&end_date=${end_date}`;

      // Add sorting parameters if provided
      if (sort_by) url += `&sort_by=${sort_by}`;
      if (sort_order) url += `&sort_order=${sort_order}`;

      // Add type parameter if provided
      if (type) url += `&type=${type}`;

      const response = await axiosInstance.get(url);
      return {
        success: true,
        data: response.data?.data || [],
        count: response.data?.count || 0,
        message: response.data?.message || 'Data fetched successfully',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message:
          error?.response?.data?.message ||
          'Failed to fetch leave balance data',
      };
    }
  },

  // Get leave consumption list
  getLeaveConsumptionList: async (params = {}) => {
    try {
      const {
        search = '',
        page = 1,
        size = 10,
        branch_id = '',
        department_id = '',
        role_id = '',
        start_date = '',
        end_date = '',
        report_mode = 'day',
        leave_type_id = '',
        sort_by = '',
        sort_order = '',
      } = params;

      let url = `${URLS?.GET_LEAVE_CONSUMPTION_REPORTS}?branch_id=${branch_id}&department_id=${department_id}&role_id=${role_id}&search=${search}&page=${page}&size=${size}&report_mode=${report_mode}`;

      // Add leave type id if provided
      if (leave_type_id) url += `&leave_type_id=${leave_type_id}`;

      // Add date range if provided
      if (start_date) url += `&start_date=${start_date}`;
      if (end_date) url += `&end_date=${end_date}`;

      // Add sorting parameters if provided
      if (sort_by) url += `&sort_by=${sort_by}`;
      if (sort_order) url += `&sort_order=${sort_order}`;

      const response = await axiosInstance.get(url);
      return {
        success: true,
        data: response.data?.data || [],
        count: response.data?.count || 0,
        message: response.data?.message || 'Data fetched successfully',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message:
          error?.response?.data?.message ||
          'Failed to fetch leave consumption data',
      };
    }
  },

  // Get leave types list
  getLeaveTypesList: async () => {
    try {
      const response = await axiosInstance.get(URLS?.GET_LEAVE_TYPE);
      return {
        success: true,
        data: response.data?.data || [],
        message: response.data?.message || 'Leave types fetched successfully',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message:
          error?.response?.data?.message || 'Failed to fetch leave types',
      };
    }
  },

  // Get role list
  getRoleList: async () => {
    try {
      const response = await axiosInstance.get(URLS?.GET_ROLE_LIST);
      return {
        success: true,
        data: response.data?.data || [],
        message: response.data?.message || 'Roles fetched successfully',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error?.response?.data?.message || 'Failed to fetch roles',
      };
    }
  },

  // Get leave usage report
  getLeaveUsageReport: async (params = {}) => {
    try {
      const { year = '', download = '' } = params;

      let url = `${URLS?.GET_LEAVE_USAGE_REPORT}`;
      const queryParams = [];

      // Add year parameter if provided
      if (year) queryParams.push(`year=${year}`);

      // Add download parameter if provided
      if (download) queryParams.push(`download=${download}`);

      // Append query parameters to URL
      if (queryParams.length > 0) {
        url += `?${queryParams.join('&')}`;
      }

      console.error('API URL:', url);
      const response = await axiosInstance.get(url, {
        responseType: download ? 'blob' : 'json',
      });

      console.error('Raw API Response:', response);
      console.error('Response Data:', response.data);

      if (download) {
        // Handle file download
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.setAttribute('download', `leave_usage_report_${year}.${download}`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);

        return {
          success: true,
          message: 'Report downloaded successfully',
        };
      }

      // The API response structure is directly in response.data, not nested under response.data.data
      const processedResponse = {
        success: true,
        data: response.data?.monthly_report || [],
        count: response.data?.monthly_report?.length || 0,
        leaveTypes: response.data?.leave_types || [],
        year: response.data?.year || '',
        message: 'Data fetched successfully',
      };

      console.error('Processed Response:', processedResponse);
      return processedResponse;
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message:
          error?.response?.data?.message ||
          'Failed to fetch leave usage report',
      };
    }
  },
};
