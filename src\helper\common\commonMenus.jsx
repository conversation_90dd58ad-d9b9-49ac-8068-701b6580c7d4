import {
  DashboardIcon,
  // AdminUser,
  BranchIcon,
  DepartmentIcon,
  Notification,
  StaffUser,
  LeaveIcon,
  MediaIcon,
  ActivityIcon,
  StaffViewIcon,
  OwnViewIcon,
  SettingIcon,
  ResignationIcon,
  DSRCenter,
  DSRViewIcon,
  ChangeRequestIcon,
  InviteIcon,
  UserListIcon,
  EMPContractIcon,
  CreateEMPContractIcon,
  PolicyTypeIcon,
  DSRDayIcon,
  DSRWeekIcon,
  DSRPayrollIcon,
  IngredientcategoryIcon,
  RecipeCategoryIcon,
  // ReportsIcon,
  // CategoryIcon,
} from '@/helper/common/images';
import SettingsIcon from '@mui/icons-material/Settings';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import PersonIcon from '@mui/icons-material/Person';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import BarChartIcon from '@mui/icons-material/BarChart';
import MovingIcon from '@mui/icons-material/Moving';
import ManageHistoryIcon from '@mui/icons-material/ManageHistory';
import UpdateIcon from '@mui/icons-material/Update';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import GppMaybeIcon from '@mui/icons-material/GppMaybe';
import RestaurantMenuIcon from '@mui/icons-material/RestaurantMenu';
import ChecklistRtlIcon from '@mui/icons-material/ChecklistRtl';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import ChangeCircleIcon from '@mui/icons-material/ChangeCircle';
import ConfirmationNumberOutlinedIcon from '@mui/icons-material/ConfirmationNumberOutlined';
import ListOutlinedIcon from '@mui/icons-material/ListOutlined';
// import KeyIcon from '@mui/icons-material/Key';
// import BrandingWatermarkIcon from '@mui/icons-material/BrandingWatermark';
// import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
// import CalculateIcon from '@mui/icons-material/Calculate';

// Components
import CompanySettings from '@/components/Setup/CompanySettings/CompanySettings';
import Department from '@/components/Setup/Department';
import HolidayManagement from '@/components/Setup/HolidayManagement';
import LeaveConfiguration from '@/components/Setup/LeaveConfiguration';
import Branch from '@/components/Setup/Branch';
import HealthAndSafety from '@/components/Setup/HealthAndSafety';
import DSRSettings from '@/components/Setup/DSRSettings';
import AdministratorAccount from '@/components/Setup/AdministratorAccounts';
import RecipeDashboard from '@/components/Recipes/RecipeDashboard';
import IngredientCategory from '@/components/Recipes/IngredientCategory';
import RecipeCategory from '@/components/Recipes/RecipeCategory';
import Allergen from '@/components/Recipes/Allergen';
import Ingredient from '@/components/Recipes/Ingredient';
import Recipes from '@/components/Recipes/Recipes';
import RecipeSettings from '@/components/Recipes/RecipeSettings';
import DownloadField from '@/components/Users/<USER>/DownloadField';
import ChangeRequest from '@/components/Setup/ChangeRequest';
import RotaReports from '@/components/Reports/RotaReports';
import DocumentsReports from '@/components/Reports/DocumentsReports';
import ActivityReports from '@/components/Reports/ActivityReports';
import ChangeRequestReports from '@/components/Reports/ChangeRequestReports';
import StaffUserReports from '@/components/Reports/StaffUserReports';
import LogBookReports from '@/components/Reports/LogBookReports';
import LeaveBalanceReports from '@/components/Reports/LeaveBalanceReports';
import LeaveConsumptionReports from '@/components/Reports/LeaveConsumptionReports';
import RecipeCTAAnalysisReports from '@/components/Reports/RecipeCTAAnalysisReports';
import ContactSubmissionReports from '@/components/Reports/ContactSubmissionReports';

export const leftSideMenu = [
  {
    id: 1,
    name: 'Dashboard',
    icon: <BarChartIcon />,
    link: '/chart-dashboard',
    permission: 'dashboard',
    submenu: [
      {
        id: 1,
        name: 'Dashboard',
        icon: DashboardIcon(),
        link: '/chart-dashboard',
        permission: 'dashboard',
      },
      {
        id: 2,
        name: 'Budget & Forecast',
        icon: <MovingIcon />,
        link: '/budget-forecast',
        permission: 'forecast',
      },
    ],
  },
  {
    id: 17,
    name: 'Setup',
    icon: SettingIcon(),
    link: '/org/setup?is_setup=1',
    permission: 'setup',
    submenu: [],
  },
  {
    id: 6,
    name: 'Staff',
    icon: StaffUser(),
    link: '/staff',
    permission: 'staff',
    submenu: [
      {
        id: 1,
        name: 'All staff',
        icon: UserListIcon(),
        link: '/staff',
        permission: 'staff',
      },
      {
        id: 2,
        name: 'Staff Invitation',
        icon: InviteIcon(),
        link: '/invited-staff',
        permission: 'user_invitation',
      },
      {
        id: 3,
        name: 'Contract Renewal',
        icon: DSRViewIcon(),
        link: '/contract-renewal',
        permission: 'employee_contract',
      },
    ],
  },
  // {
  //   id: 20,
  //   name: 'Reports',
  //   icon: <ReportsIcon />,
  //   permission: 'support_ticket',
  //   link: '/reports',
  //   submenu: [],
  // },
  {
    id: 10,
    name: 'Logs Book',
    icon: DSRCenter(),
    permission: 'dsr',
    link: '/dsr',
    // SubmenuLinks: [
    //   '/categories',
    //   '/dsr',
    //   '/dsr-request',
    //   '/wsr',
    //   '/wsr-request',
    //   '/payroll',
    //   '/payroll-request',
    //   '/dsr-report'
    // ],
    submenu: [
      {
        id: 1,
        name: 'DSR',
        icon: DSRDayIcon(),
        link: '/dsr',
        permission: 'dsr',
      },
      {
        id: 2,
        name: 'DSR Request',
        icon: DSRDayIcon(),
        link: '/dsr-request',
        permission: 'dsr',
      },
      {
        id: 3,
        name: 'WSR',
        icon: DSRWeekIcon(),
        link: '/wsr',
        permission: 'dsr',
      },
      {
        id: 4,
        name: 'WSR Request',
        icon: DSRWeekIcon(),
        link: '/wsr-request',
        permission: 'dsr',
      },
      {
        id: 5,
        name: 'Expenses',
        icon: DSRPayrollIcon(),
        link: '/payroll',
        permission: 'dsr',
      },
      {
        id: 6,
        name: 'Expenses Request',
        icon: DSRPayrollIcon(),
        link: '/payroll-request',
        permission: 'dsr',
      },
      // {
      //   id: 7,
      //   name: 'DSR Settings',
      //   icon: CategoryIcon(),
      //   link: '/categories',
      //   permission: 'dsr_report',
      // },
      {
        id: 8,
        name: 'Logbook Reports',
        icon: DSRViewIcon(),
        link: '/logbook-reports',
        permission: 'dsr',
      },
    ],
  },
  {
    id: 7,
    name: 'Leave Center',
    icon: LeaveIcon(),
    permission: 'leave_center',
    link: '/leave-remark',
    submenu: [
      {
        id: 1,
        name: 'Team Leave ',
        icon: StaffViewIcon(),
        link: '/leave-remark',
        permission: 'leave_center',
      },
      {
        id: 2,
        name: 'My Leave',
        icon: OwnViewIcon(),
        link: '/own-leave',
        permission: 'leavecenter',
      },
      // {
      //   id: 3,
      //   name: 'Leave Type & Policy',
      //   icon: PolicyTypeIcon(),
      //   permission: 'leave_setting',
      //   link: '/leave-policy-type',
      // },
      {
        id: 4,
        name: 'Leave Reports',
        icon: DSRViewIcon(),
        permission: 'leave_report',
        link: '/leave-reports',
      },
      // {
      //   id: 5,
      //   name: 'Holiday Setup',
      //   icon: SettingIcon(),
      //   permission: 'leave_setting',
      //   link: '/leave-config',
      // },
    ],
  },
  {
    id: 8,
    name: 'Rotas',
    icon: <ManageHistoryIcon />,
    link: '/rota-dashboard',
    permission: 'rota',
    submenu: [
      {
        id: 1,
        name: 'Dashboard',
        icon: <DashboardIcon />,
        link: '/rota-dashboard',
        permission: 'rota',
      },
      {
        id: 2,
        name: 'Rotas',
        icon: <UpdateIcon />,
        link: '/rotas',
        permission: 'rota',
      },
      {
        id: 3,
        name: 'Availability',
        icon: <AutorenewIcon />,
        link: '/availability',
        permission: 'rota',
      },
    ],
  },
  {
    id: 11,
    name: 'Change Req.',
    icon: ChangeRequestIcon(),
    permission: 'change_request',
    link: '/change-request',
  },
  // {
  //   id: 2,
  //   name: 'Admin User',
  //   icon: AdminUser(),
  //   link: '/adminuser',
  //   permission: 'user',
  // },
  // {
  //   id: 3,
  //   name: 'Branch',
  //   icon: BranchIcon(),
  //   link: '/branch',
  //   permission: 'branch',
  // },
  // {
  //   id: 4,
  //   name: 'Department',
  //   icon: DepartmentIcon(),
  //   link: '/department',
  //   permission: 'department',
  // },
  {
    id: 5,
    name: 'Notification',
    icon: Notification(),
    permission: 'notification',
    link: '/staff-notification',
    submenu: [
      {
        id: 1,
        name: 'Staff view',
        icon: StaffViewIcon(),
        link: '/staff-notification',
        permission: 'notification',
      },
      {
        id: 2,
        name: 'Personal view',
        icon: OwnViewIcon(),
        link: '/own-notification',
        permission: 'notificationcenter',
      },
    ],
  },
  // {
  //   id: 16,
  //   name: 'Leave Policy',
  //   icon: PolicyIcon(),
  //   permission: 'employee_contract',
  //   link: '/leave-policy',
  //   submenu: [
  //     {
  //       id: 161,
  //       name: 'Leave Type',
  //       icon: PolicyTypeIcon(),
  //       permission: 'employee_contract',
  //       link: '/policy-type'
  //     }
  //   ]
  // },
  {
    id: 14,
    name: 'Contracts',
    icon: EMPContractIcon(),
    permission: 'employee_contract',
    link: '/empcontracts',
    // link: '/leave'
    // submenu: [
    //   {
    //     id: 141,
    //     name: 'Contract Type',
    //     icon: CreateEMPContractIcon(),
    //     link: '/contract-type',
    //     permission: 'employee_contract'
    //   }
    // ]
  },
  {
    id: 15,
    name: 'Documents',
    icon: MediaIcon(),
    permission: 'category',
    link: '/document-staff/all',
    submenu: [
      {
        id: 1,
        name: 'Staff view',
        icon: CreateEMPContractIcon(),
        link: '/document-staff/all',
        permission: 'category',
      },
      {
        id: 2,
        name: 'Personal view',
        icon: OwnViewIcon(),
        link: '/document-own/all',
        permission: 'owncategory',
      },
    ],
  },
  // {
  //   id: 12,
  //   name: 'Company detail',
  //   icon: SettingIcon(),
  //   link: '/settings',
  //   permission: 'setting',
  // },
  {
    id: 18,
    name: 'Recipe',
    icon: <RestaurantMenuIcon />,
    link: '/recipes/dashboard',
    permission: 'recipe',
  },
  {
    id: 13,
    name: 'Activity Logs',
    icon: ActivityIcon(),
    link: '/activity',
    permission: 'activity_log',
  },
  {
    id: 9,
    name: 'Resignation',
    icon: ResignationIcon(),
    permission: 'resignation',
    link: '/resignation-remark',
    submenu: [
      {
        id: 1,
        name: 'Staff view',
        icon: StaffViewIcon(),
        link: '/resignation-remark',
        permission: 'resignation',
      },
      {
        id: 2,
        name: 'Personal view',
        icon: OwnViewIcon(),
        link: '/resignation',
        permission: 'resignationcenter',
      },
    ],
  },
  {
    id: 19,
    name: 'Support Ticket',
    icon: <ConfirmationNumberOutlinedIcon />,
    permission: 'support_ticket',
    link: '/support-ticket/dashboard',
    submenu: [
      {
        id: 1,
        name: 'Dashboard',
        icon: DashboardIcon(),
        link: '/support-ticket/dashboard',
        permission: 'support_ticket',
      },
      {
        id: 2,
        name: 'All Tickets',
        icon: <ListOutlinedIcon />,
        link: '/support-ticket/all-tickets',
        permission: 'support_ticket',
      },
    ],
  },
];

export const setupMenuList = [
  {
    id: 1,
    name: 'Comapny Settings',
    slug: 'company_settings',
    permission: 'setting',
    icon: <SettingsIcon />,
    tabs: [{ id: 1, label: 'Company Settings' }],
    component: <CompanySettings />,
  },
  {
    id: 2,
    name: 'Branches',
    slug: 'branches',
    permission: 'branch',
    icon: BranchIcon(),
    tabs: [{ id: 1, label: 'Branches' }],
    component: <Branch />,
  },
  {
    id: 12,
    name: 'Training Assignments',
    slug: 'training_assignments',
    permission: 'branch',
    icon: <HealthAndSafetyIcon />,
    tabs: [{ id: 1, label: 'Training Assignments' }],
    component: <HealthAndSafety />,
  },
  {
    id: 13,
    name: 'DSR settings',
    slug: 'dsr_settings',
    permission: 'dsr',
    icon: <ManageAccountsIcon />,
    tabs: [
      { id: 1, label: 'DSR Settings' },
      {
        id: 2,
        label: 'Branch DSR Settings',
        component: <DSRSettings isBrnach={true} />,
      },
    ],
    component: <DSRSettings isBrnach={false} />,
  },
  {
    id: 3,
    name: 'Departments',
    slug: 'departments',
    permission: 'department',
    icon: DepartmentIcon(),
    tabs: [{ id: 1, label: 'Departments' }],
    component: <Department />,
  },
  {
    id: 4,
    name: 'Holiday Management',
    slug: 'holidays_management',
    permission: 'leave_setting',
    icon: ActivityIcon(),
    tabs: [{ id: 1, label: 'Holidays Management' }],
    component: <HolidayManagement />,
  },
  {
    id: 5,
    name: 'Leave Configuration',
    slug: 'leave_configuration',
    permission: 'leave_setting',
    icon: PolicyTypeIcon(),
    tabs: [{ id: 1, label: 'Leave Configuration' }],
    component: <LeaveConfiguration />,
  },
  // {
  //   id: 6,
  //   name: 'Scheduling & Rota Setup',
  //   slug: 'scheduling_rota_setup',
  //   icon: <ManageHistoryIcon />,
  //   tabs: [{ id: 1, label: 'Scheduling & Rota Setup' }],
  //   component: <div>Scheduling & Rota Setup Content</div>,
  // },
  {
    id: 7,
    name: 'Administrator Accounts',
    slug: 'administrator_accounts',
    permission: 'user',
    icon: <PersonIcon />,
    tabs: [{ id: 1, label: 'Administrator Accounts' }],
    component: <AdministratorAccount />,
  },
  // {
  //   id: 8,
  //   name: 'User Roles & Permissions',
  //   slug: 'user_roles_permissions',
  //   icon: <KeyIcon />,
  //   tabs: [{ id: 1, label: 'User Roles & Permissions' }],
  //   component: <div>User Roles & Permissions Content</div>,
  // },
  // {
  //   id: 9,
  //   name: 'Branding & Communications',
  //   slug: 'branding_communications',
  //   icon: <BrandingWatermarkIcon />,
  //   tabs: [{ id: 1, label: 'Branding & Communications' }],
  //   component: <div>Branding & Communications Content</div>,
  // },
  // {
  //   id: 10,
  //   name: 'Operational Settings (DSR)',
  //   slug: 'operational_settings',
  //   icon: <SettingsSuggestIcon />,
  //   tabs: [{ id: 1, label: 'Operational Settings' }],
  //   component: <div>Operational Settings Content</div>,
  // },
  // {
  //   id: 11,
  //   name: 'Financial Setup (Budgeting)',
  //   slug: 'financial_setup',
  //   icon: <CalculateIcon />,
  //   tabs: [{ id: 1, label: 'Financial Setup' }],
  //   component: <div>Financial Setup Content</div>,
  // },
  {
    id: 8,
    name: 'Export Settings',
    slug: 'export_settings',
    permission: 'setting',
    icon: <FileDownloadIcon />,
    tabs: [{ id: 1, label: 'Export Settings' }],
    component: <DownloadField />,
  },
  {
    id: 9,
    name: 'Change Request',
    slug: 'change_request',
    permission: 'setting',
    icon: <ChangeCircleIcon />,
    tabs: [{ id: 1, label: 'Change Request' }],
    component: <ChangeRequest />,
  },
];
export const recipeMenuList = [
  {
    id: 1,
    name: 'Dashboard',
    slug: '/recipes/dashboard',
    icon: <BarChartIcon />,
    component: <RecipeDashboard />,
    permission: 'dashboard',
  },
  {
    id: 2,
    name: 'Ingredients Category',
    slug: '/recipes/ingredients-category',
    icon: <ChecklistRtlIcon />,
    component: <IngredientCategory />,
    permission: 'dashboard',
  },
  {
    id: 3,
    name: 'Recipe Category',
    slug: '/recipes/recipe-category',
    icon: <MenuBookIcon />,
    component: <RecipeCategory />,
    permission: 'dashboard',
  },
  {
    id: 4,
    name: 'Allergens',
    slug: '/recipes/allergens',
    icon: <GppMaybeIcon />,
    component: <Allergen />,
    permission: 'dashboard',
  },
  {
    id: 5,
    name: 'Ingredients',
    slug: '/recipes/ingredients',
    icon: <IngredientcategoryIcon />,
    component: <Ingredient />,
    permission: 'dashboard',
  },
  {
    id: 6,
    name: 'Recipes',
    slug: '/recipes',
    icon: <RecipeCategoryIcon />,
    component: <Recipes />,
    permission: 'dashboard',
  },
  {
    id: 7,
    name: 'Recipe Setting',
    slug: '/recipes/recipe-settings',
    icon: <SettingsIcon />,
    component: <RecipeSettings />,
    permission: 'dashboard',
  },
];

export const reportsMenuList = [
  {
    id: 1,
    name: 'Rota',
    slug: 'rota',
    permission: 'support_ticket',
    icon: <ManageHistoryIcon />,
    tabs: [{ id: 1, label: 'Rota Reports' }],
    component: <RotaReports />,
  },
  {
    id: 2,
    name: 'Document',
    slug: 'document',
    permission: 'support_ticket',
    icon: <FileDownloadIcon />,
    tabs: [{ id: 1, label: 'Document Reports' }],
    component: <DocumentsReports />,
  },
  {
    id: 3,
    name: 'Activity',
    slug: 'activity',
    permission: 'support_ticket',
    icon: <ActivityIcon />,
    tabs: [{ id: 1, label: 'Activity Reports' }],
    component: <ActivityReports />,
  },
  {
    id: 4,
    name: 'Staff',
    slug: 'staff',
    permission: 'support_ticket',
    icon: <ManageAccountsIcon />,
    tabs: [
      { id: 1, label: 'Change Request' },
      { id: 2, label: 'Staff User', component: <StaffUserReports /> },
    ],
    component: <ChangeRequestReports />,
  },
  {
    id: 5,
    name: 'Log Book',
    slug: 'log_book',
    permission: 'support_ticket',
    icon: <MenuBookIcon />,
    tabs: [{ id: 1, label: 'Log Book Reports' }],
    component: <LogBookReports />,
  },
  {
    id: 6,
    name: 'Leave',
    slug: 'leave',
    permission: 'support_ticket',
    icon: <BarChartIcon />,
    tabs: [
      {
        id: 1,
        label: 'Leave Balance Report per User',
      },
      {
        id: 2,
        label: 'Leave Consumption per Leave',
        component: <LeaveConsumptionReports />,
      },
    ],
    component: <LeaveBalanceReports />,
  },
  {
    id: 7,
    name: 'Recipe',
    slug: 'recipe',
    permission: 'support_ticket',
    icon: <RestaurantMenuIcon />,
    tabs: [
      {
        id: 1,
        label: 'Recipe CTA Analysis',
      },
      {
        id: 2,
        label: 'Contact Submission',
        component: <ContactSubmissionReports />,
      },
    ],
    component: <RecipeCTAAnalysisReports />,
  },
];
