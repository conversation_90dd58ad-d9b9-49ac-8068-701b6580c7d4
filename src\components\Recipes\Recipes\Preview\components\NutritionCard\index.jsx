import React, { useEffect, useState } from 'react';
import {
  getAttributeList,
  getPublicAttributeList,
} from '@/services/recipeService';
import { usePathname } from 'next/navigation';
import { isPublicRoute } from '@/helper/common/commonFunctions';
import NewRemovedIndicator from '../RecipeHeader/NewRemovedIndicator';
import ContentLoader from '@/components/UI/ContentLoader';
import _ from 'lodash';
import './TrafficLightNutrition.scss';

const nutrientThresholds = {
  calories: { green: 100, amber: 250, red: Infinity, unit: 'kcal' },
  sugar: { green: 5, amber: 22.5, red: Infinity, unit: 'g' },
  'added-sugar': { green: 5, amber: 22.5, red: Infinity, unit: 'g' },
  salt: { green: 0.3, amber: 1.5, red: Infinity, unit: 'g' },
  fat: { green: 3, amber: 17.5, red: Infinity, unit: 'g' },
  protein: { green: 50, amber: 100, red: Infinity, unit: 'g' },
  carbs: { green: 30, amber: 60, red: Infinity, unit: 'g' },
  carbohydrates: { green: 30, amber: 60, red: Infinity, unit: 'g' },
  fiber: { green: 10, amber: 20, red: Infinity, unit: 'g' },
  sodium: { green: 300, amber: 600, red: Infinity, unit: 'mg' },
  calcium: { green: 200, amber: 400, red: Infinity, unit: 'mg' },
  iron: { green: 3, amber: 7, red: Infinity, unit: 'mg' },
  zinc: { green: 2, amber: 5, red: Infinity, unit: 'mg' },
  cholesterol: { green: 50, amber: 100, red: Infinity, unit: 'mg' },
};

// RI (Reference Intake) per day for adults
export const referenceIntakes = {
  calories: 2000, // kcal
  sugar: 90, // g
  'added-sugar': 90, // g
  salt: 6, // g
  fat: 70, // g
  protein: 50, // g
  carbs: 260, // g
  carbohydrates: 260, // g
  fiber: 30, // g
  sodium: 2400, // mg
  calcium: 1000, // mg
  iron: 14, // mg
  zinc: 10, // mg
  cholesterol: 300, // mg
};

export function getTrafficLevel(name, value) {
  const key = name?.toLowerCase();
  if (!key) return 'low';

  const t = nutrientThresholds[key];
  if (!t) return 'low';
  if (value <= t?.green) return 'low';
  if (value <= t?.amber) return 'medium';
  return 'high';
}

// export function getRIPercentage(name, value) {
//   const key = name?.toLowerCase();
//   if (!key) return null;

//   const ri = referenceIntakes[key];
//   if (!ri) return null;
//   const percent = (value / ri) * 100;
//   return percent?.toFixed(1); // one decimal
// }

export function getRIPercentage(name, value) {
  const key = name?.toLowerCase();
  if (!key) return null;

  const ri = referenceIntakes?.[key];
  if (!ri) return null;

  const percent = (value / ri) * 100;
  const fixedPercent = percent?.toFixed(1);

  // Convert to number: removes trailing ".0" if applicable
  const result = parseFloat(fixedPercent);

  return result > 0 ? result : null;
}

function TrafficLightLabel({
  singleValue,
  name,
  value,
  unit,
  displayName,
  isHighlighted = false,
  originalValue = null,
  addedFields = {},
  removedFields = {},
}) {
  // Convert value to number and format properly
  const numericValue = parseFloat(value) || 0;
  const level = getTrafficLevel(name, numericValue);
  const riPercent = getRIPercentage(name, numericValue);

  // Map level to color class for your SCSS
  const getColorClass = (level) => {
    switch (level) {
      case 'low':
        return 'green';
      case 'medium':
        return 'amber';
      case 'high':
        return 'red';
      default:
        return 'grey';
    }
  };

  const colorClass = getColorClass(level);

  // Format value for display
  const formatValue = (val) => {
    if (val < 1 && val > 0) {
      return parseFloat(val?.toFixed(2));
    }
    return parseFloat(val?.toFixed(1));
  };

  // Helper functions to check if nutrition attribute is new or removed
  const isNutritionNew = (attrId) => {
    return (
      Array.isArray(addedFields?.nutrition_attributes) &&
      addedFields.nutrition_attributes.some((item) => item.id === attrId)
    );
  };
  const isNutritionRemoved = (attrId) => {
    return (
      Array.isArray(removedFields?.nutrition_attributes) &&
      removedFields.nutrition_attributes.some((item) => item.id === attrId)
    );
  };

  return (
    <div className={`nutrition-item nutrition-item--${colorClass}`}>
      <div className="nutrition-item__indicator-container">
        {isNutritionNew(singleValue.id) && <NewRemovedIndicator type="new" />}
        {isNutritionRemoved(singleValue.id) && (
          <NewRemovedIndicator type="removed" />
        )}
      </div>
      <span className="nutrition-item__label">{displayName || name}</span>
      <span className="nutrition-item__value">
        {formatValue(numericValue)}
        {unit}
      </span>
      {riPercent !== null && (
        <span className="nutrition-item__ri">{riPercent}%</span>
      )}
      {/* Show original value if highlighting is active and value has changed */}
      {isHighlighted &&
        originalValue !== null &&
        originalValue?.value !== numericValue && (
          <span className="highlight-original-text">
            {formatValue(originalValue?.value)}
            {originalValue?.unit || unit}
          </span>
        )}
    </div>
  );
}

function NutritionSummary({
  nutritionData,
  isHighlighted = false,
  originalNutritionData = null,
  addedFields,
  removedFields,
}) {
  // Field mapping for proper display names
  const fieldMapping = {
    'added-sugar': 'Added Sugar',
    calcium: 'Calcium',
    calories: 'Calories',
    carbohydrates: 'Carbs',
    carbs: 'Carbs',
    iron: 'Iron',
    sodium: 'Sodium',
    zinc: 'Zinc',
    protein: 'Protein',
    fat: 'Fat',
    saturates: 'Saturates',
    fiber: 'Fiber',
    sugar: 'Sugar',
    cholesterol: 'Cholesterol',
  };

  // Helper function to get original value and unit for a nutrition item
  const getOriginalValue = (currentItem) => {
    if (!originalNutritionData || !isHighlighted) return null;

    const originalItem = originalNutritionData?.find(
      (item) => item?.attribute_slug === currentItem?.attribute_slug
    );

    // Return null if no original item found or no value
    if (!originalItem || originalItem?.value === undefined) return null;

    return {
      value: parseFloat(originalItem?.value) || 0,
      unit:
        originalItem?.unit_of_measure ||
        originalItem?.unit ||
        currentItem?.unit_of_measure,
    };
  };

  return (
    <div className="traffic-light-nutrition">
      <div className="nutrition-header">Nutrition Per 100g Serving</div>
      <div className="nutrition-items-container">
        {nutritionData?.map((item) => {
          return (
            <TrafficLightLabel
              key={item?.attribute_slug}
              singleValue={item}
              name={item?.attribute_slug}
              value={item?.value || 0}
              unit={item?.unit_of_measure}
              displayName={
                fieldMapping[item?.attribute_slug] || item?.attribute_title
              }
              isHighlighted={isHighlighted}
              originalValue={getOriginalValue(item)}
              addedFields={addedFields}
              removedFields={removedFields}
            />
          );
        })}
      </div>
    </div>
  );
}

export function NutritionCard({
  recipeData,
  isHighlighted = false,
  originalNutritionData = null,
  addedFields = {},
  removedFields = {},
}) {
  const pathname = usePathname();
  // Use nutrition_attributes from API response
  const nutritionData = recipeData?.nutrition_attributes || [];
  const [allAttributes, setAllAttributes] = useState([]);
  const [loading, setLoading] = useState(true);
  const isPublic = isPublicRoute(pathname);

  const fetchAttributes = async () => {
    setLoading(true);
    try {
      // const { attributes } = await getAttributeList(
      //   '',
      //   1,
      //   { status: 'active' },
      //   '',
      //   '',
      //   'nutrition'
      // );
      let response = '';
      if (!isPublic) {
        response = await getAttributeList(
          '',
          '',
          { status: 'active' },
          '',
          '',
          'nutrition'
        );
      } else {
        response = await getPublicAttributeList(
          '',
          '',
          { status: 'active' },
          '',
          '',
          'nutrition'
        );
      }
      setAllAttributes(response?.attributes || []);
    } catch {
      setAllAttributes([]);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchAttributes();
  }, []);

  // Merge allAttributes with nutritionData
  const mergedNutrition = _(allAttributes)
    .map((attr) => {
      const found = nutritionData?.find(
        (nd) => nd?.attribute_slug === attr?.attribute_slug
      );

      return {
        ...attr,
        ...found,
        value: found
          ? `${attr?.value || ''}${attr?.value && found?.value ? ', ' : ''}${found?.value || found?.unit || ''}`
          : attr?.value || '',
        unit_of_measure: found?.unit_of_measure || attr?.unit_of_measure,
        attribute_title: attr?.attribute_title,
        attribute_slug: attr?.attribute_slug,
      };
    })
    .concat(
      nutritionData
        ?.filter(
          (nd) =>
            !allAttributes.some(
              (attr) => attr.attribute_slug === nd.attribute_slug
            )
        )
        .map((nd) => ({
          ...nd,
          value: nd?.value || nd?.unit,
        })) || []
    )
    .groupBy('attribute_slug')
    .map((items) => ({
      ...items[0],
      value: _.uniq(items.map((i) => i.value)).join(', '),
    }))
    .value();

  // Only render if there's attribute data
  if (loading)
    return (
      <div>
        <ContentLoader />
      </div>
    );
  if (!allAttributes?.length) return null;

  return (
    <NutritionSummary
      nutritionData={mergedNutrition}
      isHighlighted={isHighlighted}
      originalNutritionData={originalNutritionData}
      addedFields={addedFields}
      removedFields={removedFields}
    />
  );
}

export default NutritionCard;
