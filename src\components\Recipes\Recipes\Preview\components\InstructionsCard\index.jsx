import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import RecipesIcon from '@/components/UI/RecipePlaceholderIcon/RecipesIcon';
import NoDataView from '@/components/UI/NoDataView';
import CommonNoDataImage from '../../../CommonNoDataImage';
import NewRemovedIndicator from '../RecipeHeader/NewRemovedIndicator';
import './InstructionsCard.scss';

const InstructionsCard = ({
  instructions,
  highlightData = null,
  addedFields = {},
  removedFields = {},
}) => {
  // Check if steps are highlighted
  const isStepsHighlighted =
    highlightData?.steps && highlightData?.steps?.length > 0;

  // Helper function to render step description with highlighting
  const renderStepDescriptionWithHighlight = (instruction) => {
    if (isStepsHighlighted && highlightData?.steps) {
      // Find the corresponding old step data
      const oldStep = highlightData?.steps?.find(
        (step) => step?.recipe_step_order === instruction?.recipe_step_order
      );

      if (
        oldStep &&
        oldStep?.recipe_step_description !==
          instruction?.recipe_step_description
      ) {
        return (
          <div className="highlight-container">
            <div
              className="instructions-card__step-text highlight-no-margin-bottom"
              dangerouslySetInnerHTML={{
                __html: instruction?.recipe_step_description || '',
              }}
            />
            <div
              className="highlight-original-text"
              dangerouslySetInnerHTML={{
                __html: oldStep?.recipe_step_description || '',
              }}
            />
          </div>
        );
      }
    }

    // Normal display
    return (
      <div
        className="instructions-card__step-text"
        dangerouslySetInnerHTML={{
          __html: instruction?.recipe_step_description || '',
        }}
      />
    );
  };

  // Helper functions to check if step is new or removed
  const isStepNew = (stepId) => {
    return (
      Array.isArray(addedFields?.steps) &&
      addedFields.steps.some((item) => item.id === stepId)
    );
  };
  const isStepRemoved = (stepId) => {
    return (
      Array.isArray(removedFields?.steps) &&
      removedFields.steps.some((item) => item.id === stepId)
    );
  };

  return (
    <div className="instructions-card">
      <div className="instructions-card__header">
        <p
          className={`instructions-card__title ${isStepsHighlighted ? 'highlight-no-margin-bottom' : ''}`}
        >
          <Icon name="List" size={20} color="currentColor" />
          <span>Cooking Instructions</span>
        </p>
      </div>

      <div className="instructions-card__content">
        {!instructions || instructions?.length === 0 ? (
          <NoDataView
            image={<CommonNoDataImage />}
            title="No Instructions Available"
            description="There are no cooking instructions available for this recipe at the moment."
            className="no-data-auto-height-conainer"
          />
        ) : (
          <div className="instructions-card__list">
            {instructions?.map((instruction) => (
              <div
                key={instruction?.id}
                className="instructions-card__item d-flex justify-space-between gap-sm"
              >
                {isStepNew(instruction?.id) && (
                  <NewRemovedIndicator type="new" />
                )}
                {isStepRemoved(instruction?.id) && (
                  <NewRemovedIndicator type="removed" />
                )}
                <div className="instructions-card__step-header">
                  <div className="instructions-card__step-number-wrap">
                    <span>{instruction?.recipe_step_order}</span>
                  </div>
                  <div className="instructions-card__step-content">
                    <div className="instructions-card__image-wrap">
                      {instruction?.item_detail?.item_link && (
                        <RecipesIcon
                          iconUrl={instruction?.item_detail?.item_link}
                          altText={`step_${instruction?.recipe_step_order}`}
                          imgWidth={80}
                          imgHeight={80}
                          className="instructions-card__image"
                        />
                      )}
                    </div>
                    {renderStepDescriptionWithHighlight(instruction)}

                    {/* <div className="instructions-card__details">
                      <div className="instructions-card__detail instructions-card__detail--timing">
                        <div className="instructions-card__detail-header">
                          <Icon
                            name="Clock"
                            size={14}
                            color="var(--color-warning)"
                          />
                          <span className="instructions-card__detail-label instructions-card__detail-label--timing">
                            Timing
                          </span>
                        </div>
                        <p className="instructions-card__detail-text">
                          {instruction?.timing}
                        </p>
                      </div>

                      <div className="instructions-card__detail instructions-card__detail--equipment">
                        <div className="instructions-card__detail-header">
                          <Icon
                            name="Wrench"
                            size={14}
                            color="var(--color-success)"
                          />
                          <span className="instructions-card__detail-label instructions-card__detail-label--equipment">
                            Equipment
                          </span>
                        </div>
                        <p className="instructions-card__detail-text">
                          {instruction?.equipment}
                        </p>
                      </div>
                    </div>

                    {instruction?.chefNotes && (
                      <div className="instructions-card__note instructions-card__note--chef">
                        <div className="instructions-card__note-header">
                          <Icon
                            name="ChefHat"
                            size={14}
                            color="var(--color-primary)"
                            className="instructions-card__note-icon"
                          />
                          <span className="instructions-card__note-label instructions-card__note-label--chef">
                            Chef Notes:
                          </span>
                        </div>
                        <p className="instructions-card__note-text">
                          {instruction?.chefNotes}
                        </p>
                      </div>
                    )}

                    {instruction?.qualityCheck && (
                      <div className="instructions-card__note instructions-card__note--quality">
                        <div className="instructions-card__note-header">
                          <Icon
                            name="CheckCircle"
                            size={14}
                            color="var(--color-success)"
                            className="instructions-card__note-icon"
                          />
                          <span className="instructions-card__note-label instructions-card__note-label--quality">
                            Quality Check:
                          </span>
                        </div>
                        <p className="instructions-card__note-text">
                          {instruction?.qualityCheck}
                        </p>
                      </div>
                    )} */}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default InstructionsCard;
