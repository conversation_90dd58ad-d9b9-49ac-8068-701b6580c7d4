'use client';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { Box } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import ContentLoader from '@/components/UI/ContentLoader';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { leaveService } from '@/services/leaveService';
import '../reports.scss';

export default function LeaveConsumptionReports() {
  // State management
  const [loader, setLoader] = useState(false);
  const [leaveConsumptionList, setLeaveConsumptionList] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({});
  const [leaveTypes, setLeaveTypes] = useState([]);

  // Filter fields configuration - simplified to only year filter
  const filterFields = useMemo(
    () => [
      {
        type: 'select',
        label: 'Year',
        name: 'year',
        placeholder: 'Select Year',
        options: [
          { label: '2024', value: '2024' },
          { label: '2025', value: '2025' },
          { label: '2026', value: '2026' },
        ],
      },
    ],
    []
  );

  // Dynamic columns configuration based on API response
  const columns = useMemo(() => {
    const baseColumns = [
      {
        header: 'Month',
        accessor: 'month',
        sortable: false,
        renderCell: (value) =>
          value !== null && value !== undefined ? value : '-',
      },
    ];

    // Add dynamic columns based on leave types from API
    const leaveTypeColumns = leaveTypes.map((leaveType) => ({
      header: leaveType.name,
      accessor: leaveType.key,
      sortable: false,
      renderCell: (value) => {
        // Show the actual value if it exists, otherwise show "-"
        if (value !== null && value !== undefined) {
          return value;
        }
        return '-';
      },
    }));

    return [...baseColumns, ...leaveTypeColumns];
  }, [leaveTypes]);

  // Get leave usage report using new API
  const getLeaveUsageReport = useCallback(
    async (year = '', showLoader = true) => {
      if (showLoader) setLoader(true);
      try {
        const params = {
          year,
        };

        const response = await leaveService.getLeaveUsageReport(params);

        if (response.success) {
          setLeaveConsumptionList(response.data || []);
          setTotalCount(response.count || 0);
          setLeaveTypes(response.leaveTypes || []);
        } else {
          setLeaveConsumptionList([]);
          setTotalCount(0);
          setLeaveTypes([]);
          setApiMessage('error', response.message);
        }
        setLoader(false);
      } catch (error) {
        console.error('API Call Error:', error);
        setLoader(false);
        setLeaveConsumptionList([]);
        setTotalCount(0);
        setLeaveTypes([]);
        setApiMessage(
          'error',
          error?.message || 'Failed to fetch leave usage report'
        );
      }
    },
    []
  );

  // Download leave usage report as PDF
  const downloadLeaveUsageReport = useCallback(async (year = '') => {
    try {
      const params = {
        year,
        download: 'pdf',
      };

      const response = await leaveService.getLeaveUsageReport(params);

      if (response.success) {
        setApiMessage('success', response.message);
      } else {
        setApiMessage('error', response.message);
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.message || 'Failed to download leave usage report'
      );
    }
  }, []);

  // Handle filter application - simplified for year filter only
  const handleApplyFilters = (values) => {
    setFilters(values);
    getLeaveUsageReport(values?.year || '', false);
  };

  // Handle download PDF
  const handleDownloadPDF = () => {
    const year = filters?.year || '2025';
    downloadLeaveUsageReport(year);
  };

  // useEffect hooks
  useEffect(() => {
    // Load initial data with default year 2025
    getLeaveUsageReport('2025');
  }, [getLeaveUsageReport]);

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        buttonText="Apply Filters"
        initialValues={filters}
        additionalButtons={[
          {
            text: 'Download PDF',
            onClick: handleDownloadPDF,
            variant: 'outlined',
          },
        ]}
      />

      <Box className="report-table-container">
        {loader ? (
          <ContentLoader />
        ) : (
          <CommonTable
            columns={columns}
            data={leaveConsumptionList}
            totalCount={totalCount}
            showPagination={false}
          />
        )}
      </Box>
    </Box>
  );
}
