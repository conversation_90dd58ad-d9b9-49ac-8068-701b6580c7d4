'use client';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { Box } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import ContentLoader from '@/components/UI/ContentLoader';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { leaveService } from '@/services/leaveService';
import '../reports.scss';

export default function LeaveConsumptionReports() {
  // State management
  const [loader, setLoader] = useState(false);
  const [leaveConsumptionList, setLeaveConsumptionList] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({});

  // Filter fields configuration - simplified to only year filter
  const filterFields = useMemo(
    () => [
      {
        type: 'select',
        label: 'Year',
        name: 'year',
        placeholder: 'Select Year',
        options: [
          { label: '2024', value: '2024' },
          { label: '2025', value: '2025' },
          { label: '2026', value: '2026' },
        ],
      },
    ],
    []
  );

  // CommonTable columns configuration - removed ID, User, Branch/Dept, Used Leave as requested
  const columns = [
    {
      header: 'Month',
      accessor: 'month',
      sortable: false,
      renderCell: (value) =>
        value !== null && value !== undefined ? value : '-',
    },
    {
      header: 'Sick Leave',
      accessor: 'sick_leave',
      sortable: false,
      renderCell: (value) =>
        value !== null && value !== undefined ? value : '0',
    },
    {
      header: 'Casual Leave',
      accessor: 'casual_leave',
      sortable: false,
      renderCell: (value) =>
        value !== null && value !== undefined ? value : '0',
    },
    {
      header: 'Earned Leave',
      accessor: 'earned_leave',
      sortable: false,
      renderCell: (value) =>
        value !== null && value !== undefined ? value : '0',
    },
    {
      header: 'Comp Off',
      accessor: 'comp_off',
      sortable: false,
      renderCell: (value) =>
        value !== null && value !== undefined ? value : '0',
    },
    {
      header: 'Unpaid Leave',
      accessor: 'unpaid_leave',
      sortable: false,
      renderCell: (value) =>
        value !== null && value !== undefined ? value : '0',
    },
  ];

  // Get leave usage report using new API
  const getLeaveUsageReport = useCallback(
    async (year = '', showLoader = true) => {
      if (showLoader) setLoader(true);
      try {
        const params = {
          year,
        };

        const response = await leaveService.getLeaveUsageReport(params);

        if (response.success) {
          setLeaveConsumptionList(response.data);
          setTotalCount(response.count);
        } else {
          setLeaveConsumptionList([]);
          setTotalCount(0);
          setApiMessage('error', response.message);
        }
        setLoader(false);
      } catch (error) {
        setLoader(false);
        setLeaveConsumptionList([]);
        setTotalCount(0);
        setApiMessage(
          'error',
          error?.message || 'Failed to fetch leave usage report'
        );
      }
    },
    []
  );

  // Download leave usage report as PDF
  const downloadLeaveUsageReport = useCallback(async (year = '') => {
    try {
      const params = {
        year,
        download: 'pdf',
      };

      const response = await leaveService.getLeaveUsageReport(params);

      if (response.success) {
        setApiMessage('success', response.message);
      } else {
        setApiMessage('error', response.message);
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.message || 'Failed to download leave usage report'
      );
    }
  }, []);

  // Handle filter application - simplified for year filter only
  const handleApplyFilters = (values) => {
    setFilters(values);
    getLeaveUsageReport(values?.year || '', false);
  };

  // Handle download PDF
  const handleDownloadPDF = () => {
    const year = filters?.year || '2025';
    downloadLeaveUsageReport(year);
  };

  // useEffect hooks
  useEffect(() => {
    // Load initial data with default year 2025
    getLeaveUsageReport('2025');
  }, [getLeaveUsageReport]);

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        buttonText="Apply Filters"
        initialValues={filters}
        additionalButtons={[
          {
            text: 'Download PDF',
            onClick: handleDownloadPDF,
            variant: 'outlined',
          },
        ]}
      />

      <Box className="report-table-container">
        {loader ? (
          <ContentLoader />
        ) : (
          <CommonTable
            columns={columns}
            data={leaveConsumptionList}
            totalCount={totalCount}
            showPagination={false}
          />
        )}
      </Box>
    </Box>
  );
}
